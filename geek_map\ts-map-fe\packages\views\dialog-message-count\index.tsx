/* ! <AUTHOR> at 2022/08/31 */
import moment from "moment";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Modal, Tabs, Alert, Table, DatePicker, Button, message } from "antd";
import { InfoCircleOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { $eventBus } from "../../singleton";

const todayRange: any = [moment(), moment()];
const { RangePicker } = DatePicker;
type PropsType = {
  messageCount: { backLogCount: number; notificationCount: number };
};
function DialogMessageCount(props: PropsType) {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [currentTab, setCurrentTab] = useState(undefined); // msg | wait
  const [msgCount, setMsgCount] = useState(0);
  const [waitCount, setWaitCount] = useState(0);
  const [todayBacklogCount, setTodayBacklogCount] = useState(0);
  const [todayWarnCount, setTodayWarnCount] = useState(0);
  const [todayErrorCount, setTodayErrorCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [filter, setFilter] = useState({ eventType: "", eventLevel: "", isRead: "" });
  const [tableList, setTableList] = useState([]);
  const [dateRange, setDateRage] = useState(todayRange);

  // 接受ws推送信息
  useEffect(() => {
    if (!props.messageCount) return;
    const { notificationCount, backLogCount } = props.messageCount;
    setMsgCount(notificationCount || 0);
    setWaitCount(backLogCount || 0);
  }, [props.messageCount]);

  // dialog展示
  useEffect(() => {
    $eventBus.on("dialogMessageCountShow", params => {
      setCurrentTab(params.type);
      setVisible(true);
    });

    return () => {
      $eventBus.off("dialogMessageCountShow");
    };
  }, []);

  // 数据初始化
  useEffect(() => {
    if (!visible) return;
    getNumber();
    return () => {
      reset();
    };
  }, [visible]);

  // 重新请求list
  useEffect(() => {
    getTableList();
  }, [currentTab, visible]);

  const handleTableChange = (newPagination: any, filters: any, sorter: any) => {
    const { eventType, msgOperation, eventLevel } = filters;
    if (eventType) filter.eventType = filters.eventType.pop();
    else filter.eventType = "";
    if (eventLevel) filter.eventLevel = filters.eventLevel.pop();
    else filter.eventLevel = "";
    if (msgOperation) filter.isRead = filters.msgOperation.pop();
    else filter.isRead = "";
    setFilter({ ...filter });
    setPagination(newPagination);

    getTableList(newPagination, { ...filter });
  };

  const getTableList = (p?: any, f?: any) => {
    if (!currentTab || !visible) return;

    let page = Object.assign({}, pagination, p);
    const filters = Object.assign({}, filter, f);

    setLoading(true);
    let params: any = {
      pageSize: page.pageSize,
      currentPage: page.current,
      eventGroup: currentTab === "msg" ? 1 : 2,
      eventType: filters.eventType,
      eventLevel: filters.eventLevel,
      isRead: filters.isRead,
    };

    if (currentTab === "msg") {
      if (!dateRange || dateRange.length != 2) {
        message.error(t("lang.rms.fed.selectDate"));
        setTableList([]);
        setLoading(false);
        return;
      }
      params.startTime = dateRange[0].startOf("day").format("x");
      params.endTime = dateRange[1].endOf("day").format("x");
    }

    let arr = [];
    for (let key in params) {
      arr.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
    }
    params = arr.length > 0 ? arr.join("&") : "";
    _$utils
      .reqPost("/athena/warehouse/monitor/queryEvent", params, {
        "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
      })
      .then(res => {
        if (res.code === 0) {
          const data = res.data || {};
          setLoading(false);

          page.total = data.recordCount;
          setPagination({ ...page });
          setTableList(data.recordList || []);
        }
      });
  };

  const getNumber = () => {
    _$utils.reqGet("/athena/warehouse/monitor/statistics").then(res => {
      if (res.code === 0) {
        const data = res.data || {};
        setTodayBacklogCount(data?.backlogCount || 0);
        setTodayWarnCount(data?.warnCount || 0);
        setTodayErrorCount(data?.errorCount || 0);
      }
    });
  };

  const reset = () => {
    setTableList([]);
    setPagination({ current: 1, pageSize: 10, total: 0 });
    setFilter({ eventType: "", eventLevel: "", isRead: "" });
  };

  const onRead = (row: any) => {
    _$utils
      .reqPost("/athena/warehouse/monitor/changeToRead", `eventId=${row.id}`, {
        "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
      })
      .then(res => {
        if (res.code === 0) {
          tableList.find(item => item.id === row.id).isRead = 1;
          setTableList(tableList || []);

          // 通知小喇叭组件，消息已读，可以消失
          $eventBus.emit("messageMarkedAsRead", { eventId: row.id });
        }
      });
  };

  const exportFn = () => {
    if (currentTab !== "msg") return;
    if (!dateRange || dateRange.length != 2) {
      message.error(t("lang.rms.fed.selectDate"));
      return;
    }
    let params: any = {
      pageSize: 100000,
      currentPage: 1,
      eventGroup: currentTab === "msg" ? 1 : 2,
      eventType: filter.eventType,
      eventLevel: filter.eventLevel,
      isRead: filter.isRead,
      language: _$utils.getLocalLang(),
    };
    params.startTime = dateRange[0].startOf("day").format("x");
    params.endTime = dateRange[1].endOf("day").format("x");
    _$utils.reqGet("/athena/warehouse/monitor/export", params).then(res => {
      if (!__rms_env_conf.isPro) {
        window.open(__rms_env_conf.API_URL + res.data);
      } else {
        window.open(window.location.origin + res.data);
      }
    });
  };
  const exportFnAll = () => {
    if (currentTab !== "msg") return;
    if (!dateRange || dateRange.length != 2) {
      message.error(t("lang.rms.fed.selectDate"));
      return;
    }
    let params: any = {
      pageSize: pagination.pageSize,
      currentPage: pagination.current,
      eventGroup: currentTab === "msg" ? 1 : 2,
      eventType: filter.eventType,
      eventLevel: filter.eventLevel,
      isRead: filter.isRead,
      language: _$utils.getLocalLang(),
    };
    // params.startTime = dateRange[0].startOf("day").format("x");
    // params.endTime = dateRange[1].endOf("day").format("x");
    _$utils.reqGet("/athena/warehouse/monitor/export", params).then(res => {
      if (!__rms_env_conf.isPro) {
        window.open(__rms_env_conf.API_URL + res.data);
      } else {
        window.open(window.location.origin + res.data);
      }
    });
  };

  const columns: any = [
    { title: t("lang.rms.fed.startTime"), dataIndex: "createTime", width: 180 },
    { title: t("lang.rms.fed.endTime"), dataIndex: "finishTime", width: 180 },
    {
      title: t("lang.rms.fed.listType"),
      dataIndex: "eventType",
      width: 80,
      filterMultiple: false,
      filters: [
        { text: t("lang.rms.fed.optionShelf"), value: 7 },
        { text: t("lang.rms.fed.optionCharger"), value: 5 },
        { text: t("lang.rms.fed.optionSystem"), value: 8 },
        { text: t("lang.rms.fed.optionRobot"), value: 6 },
      ],
      render: (text: any) => {
        if (!text) return "";
        return t(text);
      },
    },
    {
      title: t("lang.rms.fed.listObject"),
      dataIndex: "eventObj",
      width: 120,
      render: (text: any) => {
        if (!text || text === "null") return "";
        return <span style={{ color: "#0099ff" }}>{text}</span>;
      },
    },
    {
      title: t("lang.rms.fed.listLevel"),
      dataIndex: "eventLevel",
      width: 80,
      filterMultiple: false,
      filters: [
        { text: t("lang.rms.fed.optionError"), value: 2 },
        { text: t("lang.rms.fed.optionWarning"), value: 1 },
        { text: t("lang.rms.fed.optionNormal"), value: 0 },
      ],
      render: (text: any) => t(text),
    },
    {
      title: currentTab === "msg" ? t("lang.rms.fed.buttonMessage") : t("lang.rms.fed.listItem"),
      dataIndex: "eventContent",
      render: (text: any) => _$utils.transMsgLang(text),
    },
    {
      title: t("lang.rms.fed.textOperation"),
      dataIndex: "msgOperation",
      width: 80,
      filterMultiple: false,
      filters:
        currentTab === "msg"
          ? [
              { text: t("lang.rms.fed.optionRead"), value: "1" },
              { text: t("lang.rms.fed.unread"), value: "0" },
            ]
          : [
              { text: t("lang.rms.fed.optionHandled"), value: "1" },
              { text: t("lang.rms.fed.unprocessed"), value: "0" },
            ],
      render: (text: any, row: any) => {
        return (
          <>
            <Button
              size="small"
              type="link"
              onClick={() => {
                onRead(row);
              }}
              style={{ display: row.isRead === 0 ? "inline-block" : "none" }}
            >
              {currentTab === "msg" ? t("lang.rms.fed.unread") : t("lang.rms.fed.unprocessed")}
            </Button>
            <Button
              size="small"
              type="link"
              disabled={row.isRead === 1}
              style={{ display: row.isRead === 1 ? "inline-block" : "none" }}
            >
              {currentTab === "msg"
                ? t("lang.rms.fed.optionRead")
                : t("lang.rms.fed.optionHandled")}
            </Button>
          </>
        );
      },
    },
  ];

  const items = [
    {
      key: "msg",
      label: (
        <span>
          <InfoCircleOutlined style={{ marginRight: "5px" }} />
          {t("lang.rms.fed.tabMessage")}
        </span>
      ),
      children: (
        <>
          <Alert
            message={
              <div className="alert-content">
                <span>
                  {t("lang.rms.fed.textUnreadMessage")} :
                  <em style={{ color: "#108ee9" }}>{msgCount}</em>
                </span>
                <span>
                  {t("lang.rms.fed.textTodayErrorMessage")} :
                  <em style={{ color: "#fc1346" }}>{todayErrorCount}</em>
                </span>
                <span>
                  {t("lang.rms.fed.textWarningMessage")} :
                  <em style={{ color: "#fcb067" }}>{todayWarnCount}</em>
                </span>
              </div>
            }
            type="info"
            showIcon
          />
          <div className="time-box">
            <div>
              <span>{t("lang.rms.fed.selectDate")}</span>
              <RangePicker
                defaultValue={todayRange}
                style={{ margin: "0 10px" }}
                size="small"
                inputReadOnly
                onChange={dates => {
                  setDateRage(dates);
                }}
              />
              <Button
                size="small"
                type="primary"
                onClick={() => {
                  const pageObj = { current: 1, pageSize: 10, total: 0 };
                  setPagination(pageObj);
                  getTableList(pageObj);
                }}
              >
                {t("lang.rms.fed.query")}
              </Button>
            </div>
            <div>
            <Button size="small"  onClick={exportFnAll} style={{ marginRight: "10px" }}>
              {t("lang.rms.fed.buttonWhole")}
              {t("lang.rms.fed.buttonExport")}
            </Button>
            <Button size="small" type="primary" onClick={exportFn}>
              {t("lang.rms.fed.buttonExport")}
            </Button>
            </div>
          </div>
        </>
      ),
    },
    {
      key: "wait",
      label: (
        <span>
          <ClockCircleOutlined style={{ marginRight: "5px" }} />
          {t("lang.rms.fed.tabToDo")}
        </span>
      ),
      children: (
        <Alert
          message={
            <div className="alert-content">
              <span>
                {t("lang.rms.fed.tabToDo")} :<em style={{ color: "#108ee9" }}>{waitCount}</em>
              </span>
              <span>
                {t("lang.rms.fed.textTodayToDo")} :
                <em style={{ color: "#fc1346" }}>{todayBacklogCount}</em>
              </span>
            </div>
          }
          type="info"
          showIcon
        />
      ),
    },
  ];

  return (
    <Modal
      title={null}
      centered
      open={visible}
      footer={null}
      width={"80%"}
      maskClosable={false}
      onCancel={() => setVisible(false)}
      wrapClassName="map2d-dialog-msg-wait"
      style={{ minWidth: 600 }}
    >
      <Tabs
        activeKey={currentTab}
        onTabClick={key => {
          setCurrentTab(key);
          reset();
        }}
        items={items}
        tabBarStyle={{ marginBottom: 6 }}
        style={{ position: "sticky", top: 0, zIndex: 9, background: "#fff" }}
      />
      <Table
        dataSource={tableList}
        rowKey="id"
        columns={columns}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        size="small"
        bordered
        style={{ marginTop: 8, width: "100%", minHeight: 360 }}
      />
    </Modal>
  );
}

export default DialogMessageCount;
