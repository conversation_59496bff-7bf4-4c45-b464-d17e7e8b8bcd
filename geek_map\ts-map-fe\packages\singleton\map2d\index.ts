import MapRender from "../../../src/MapRender";
import MapWorker from "../../../src/MapWorker";
import $eventBus from "../eventBus/index";
class Map2D implements Monitor2D.Main {
  mapRender: MRender.Main;
  mapWorker: MWorker.Main;

  constructor($dom: HTMLElement, options: Monitor2D.MapOptions) {
    if (!$dom) throw new Error("没有dom");
    this._createMapDom($dom, options?.domOffset || []);

    let mapWorker = new MapWorker(options?.wsUrl || "");
    mapWorker.onCallBack(this.wsDataReady.bind(this)); // 针对init数据返回
    this.mapWorker = mapWorker;

    let mapRender = new MapRender($dom);
    mapRender.ready(() => this.mapWorker.reqFloors()); // 地图准备好之后请求floor数据
    mapRender.rendered(this.mapRendered.bind(this)); // 当前帧渲染完成
    mapRender.click(this.mapClick.bind(this)); // 当前帧渲染完成
    this.mapRender = mapRender;

    this.mapWorker.init();
    this.mapRender.init();
  }

  private wsDataReady(dataType: MWorker.dataType, data: any) {
    switch (dataType) {
      case "wsInitFloors":
        if (data?.floorList) {
          $eventBus.emit("wsFloorListTop", data.floorList);
        }
        $eventBus.emit("wsMapReleased", { mapReleased: data.released });
        this.mapRender.renderFloors(data.floorsData);
        break;
      case "wsInitDisplay":
        this.mapRender.renderDisplays(data);
        if (data?.isInitFinish) {
          this.mapWorker
            .reqSocket("FastSearchRequestMsg", { floorIds: [], searchTypes: [] })
            .then(res => {
              if (res?.msgType !== "FastSearchResponseMsg") return;
              if (res?.body?.code === 0) {
                $eventBus.emit("wsFastSearchTop", res?.body?.data || []);
              }
            });
        }
        break;
      case "wsUpdateDisplay":
        this.mapRender.updateDisplays(data);
        break;
      case "wsLogicAreasDisplay":
        $eventBus.emit("wsDataLogicAreas", data);

        let areaIds: Array<code> = [];
        data.forEach((item: any) => {
          if (item.systemState !== "STOP" && item.systemState !== "FIRESTOP") return;
          if (!item.logicId) return;
          areaIds.push(item.logicId.toString());
        });
        this.mapRender.toggleLayer("stopArea", true, areaIds);
        break;
      case "wsSpeedLimitAreasDisplay":
        $eventBus.emit("wsDataSpeedLimitAreas", data);

        let speedLimitAreaIds: Array<code> = [];
        data.forEach((item: any) => {
          if (item.active !== true) return;
          if (!item.areaId) return;
          speedLimitAreaIds.push(item.areaId.toString());
        });
        this.mapRender.toggleLayer("speedLimitArea", true, speedLimitAreaIds);
        break;

      case "wsQueryData": // 各元素数据实时查询推送
        $eventBus.emit("wsDataQueryRightTab", data);
        break;
      case "wsMessageCount": // 消息、待办
        $eventBus.emit("wsMessageCount", data);
        break;
      case "wsMessageDevice": // 推送设备异常信息 数组格式[] 左上角透明层内容
        $eventBus.emit("wsMessageDeviceLeftTop", data || []);
         //与高老板沟通 语音播报需要的 根据异常信息显示
        $eventBus.emit("wsMessageCardAudio", data || []);
        break;
      case "wsMessageRobot": // 异常机器人详细信息
        $eventBus.emit("wsMessageRobotLeftTop", data?.stat || {});
        $eventBus.emit("wsMessageRobotLeft", data?.result || []);
        break;
      case "wsMessageTask": // 机器人任务详细信息
        $eventBus.emit("wsMessageTaskLeft", data?.stat || {});
        break;
      case "wsDeadLockRobots": // 死锁机器人
        $eventBus.emit("wsDeadLockRobotsRight", data);
        break;
      case "wsMessageCard": // 消息提醒（卡片事件推送） 语音播报需要的 逐条消息
        // $eventBus.emit("wsMessageCardAudio", data);
        break;
      case "wsMapConfig": // mapConfig
        // mapConfig
        if (data?.config) {
          $eventBus.emit("wsMapConfigRight", data.config);
        }
        // warning 大红框
        if (data?.systemWarning) {
          let systemWarning = data.systemWarning;
          if (systemWarning === "normal") systemWarning = "transparent";
          $eventBus.emit("wsSystemWarningBox", systemWarning);
        }
        break;
    }
  }

  private mapClick(data: MRender.clickParams) {
    if (!data?.layer) return;
    switch (data?.layer) {
      case "realtimeObstacle":
        $eventBus.emit("mapClickRealtimeObstacle", data);
        break;
      case "knockArea":
        $eventBus.emit("mapClickKnockArea", data);
        break;
      default:
        $eventBus.emit("mapClick", data);
        break;
    }
  }

  private mapRendered(renderType: MRender.renderedType, res?: any) {
    switch (renderType) {
      case "floorRendered":
        $eventBus.emit("wsMapLoading", { loading: false });
        $eventBus.emit("wsMapFloorChangeRight", { floorChanging: false });
        break;
      case "displayRendered":
        this.mapWorker.reqUpdate();
        break;
      case "mapPosition":
        const type = res?.type;
        if (type === "stations") $eventBus.emit("mapStationPosition", res);
        break;
    }
  }

  private _createMapDom($dom: HTMLElement, domOffset: Array<number>) {
    let offset = [0, 0, 0, 0];
    for (let i = 0; i < 4; i++) {
      if (domOffset[i]) offset[i] = domOffset[i];
    }
    const style: { [propName: string]: any } = {
      position: "absolute",
      top: offset[0] + "px",
      right: offset[1] + "px",
      bottom: offset[2] + "px",
      left: offset[3] + "px",
      margin: "auto",
      overflow: "hidden",
      background: "#eee",
    };

    let $mapStyle: { [propName: string]: any } = $dom.style;
    for (let key in style) {
      $mapStyle[key] = style[key];
    }

    return $dom;
  }

  destroy(): void {
    if (this.mapRender) {
      this.mapRender.destroy();
      this.mapRender = undefined;
    }
    if (this.mapWorker) {
      this.mapWorker.destroy();
      this.mapWorker = undefined;
    }
  }
}

export default Map2D;
